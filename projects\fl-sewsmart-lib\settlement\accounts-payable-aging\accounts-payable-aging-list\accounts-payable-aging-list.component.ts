import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { accountsPayableAgingSearchList, accountsPayableAgingTableHeader, age_day_list } from '../models/accounts-payable-aging.config';
import { AccountsPayableAgingService } from '../accounts-payable-aging.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-receivable-inquiry-list',
  templateUrl: './accounts-payable-aging-list.component.html',
  styleUrls: ['./accounts-payable-aging-list.component.scss']
})
export class AccountsPayableAgingListComponent implements OnInit {
  @ViewChild('searchBarWrap') searchBarWrap!: ElementRef<HTMLElement>;

  constructor(
    private _cdRef: ChangeDetectorRef,
    private _service: AccountsPayableAgingService,
    private _router: Router,
    private _translate: TranslateService,
  ) { }

  // 筛选配置config
  searchList = accountsPayableAgingSearchList;
  /** 筛选项下拉数据 */
  searchOption?: any
  // 需要筛选的数据
  searchData = this.initSearchData();

  // table表头配置
  tableHeader = accountsPayableAgingTableHeader;
  // table表格配置
  tableConfig: any = {
    translateName: "accountsPayableAging.",
    detailBtn: false,
    dataList: [],
    count: null,
    height: 600,
    loading: false,
    tableName: 'accountsPayableAgingList',
    hasAction: false,
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '120px',
  };
  /**
   * 查询类型
   * @description 1是应收明细 2是应收汇总
   */
  queryType: 1 | 2 = 1;

  ngOnInit() {
    this.getOptionList()
    this.getList(true)
  }

  ngAfterViewInit() {
    this.calcTableHeight()
  }

  // 初始化请求参数
  initSearchData() {
    return {
      supplier_name: null,
      contract_number: null,
      age_day: null,
      account_type: [1,2],
      unpaid_money: true
    }
  }

  // 筛选项改变的时候的操作
  searchDataChange(key: string) {
    if (key == 'supplier_name') {
      this.searchData.contract_number = null;
      this.getOptionList()
    }
    this.getList(true)
  }
  // 获取选项下拉数据
  getOptionList() {
    this._service.getOptionList(this.searchData?.supplier_name ?? null)?.subscribe((res: any) => {
      if (res.code == 200) {
        this.searchOption = {
          ...res?.data,
          age_day_list
        }
      }
    })
  }

  // 类型切换
  queryTypeChange(value: 1 | 2) {
    this.queryType = value
    if (value == 2) {
      const showHeader = ['supplier_name', 'contract_money', 'invoice_money', 'no_invoice_money', 'paid_money', 'unpaid_money']
      this.tableHeader = accountsPayableAgingTableHeader?.filter((item) => showHeader?.includes(item.key))
    } else {
      this.tableHeader = accountsPayableAgingTableHeader
    }
    this.searchList?.forEach((item) => value == 2 && ['contract_number', 'age_day_list'].includes(item.labelKey) ? item.isShow = false : item.isShow = true)
    this.getList(true)
  }

  // 物料或者加工
  checkAccountTypeChange(value: boolean, type: 1 | 2) {
    this.searchData.account_type = value ? [...this.searchData?.account_type, type] : this.searchData?.account_type?.filter(item => item != type)
    this.getList(true)
  }

  // 查询对应的数据
  queryRowData(key: string, item: any) {
    if (key != 'contract_number') {
      return
    }
    // 1是物料 2是加工
    const urlInfo = item?.account_type == 1 ? {
      url: '/material-procurement/order/list/',
      id: item?.procurement_id
    } : {
      url: '/outsourcing-manage/garment-outsourcing/list/',
      id: item?.bulk_order_id,
      data: {
        io_id: item?.io_id
      }
    }
    this._router.navigate([urlInfo?.url, urlInfo?.id], urlInfo?.data && { queryParams: urlInfo?.data })
  }

  // 获取对应的合并项中的单元格数据
  getColName(key: string, item: any) {
    if (key == 'account_type') {
      return item?.account_type == 1 ? this._translate.instant('accountsPayableAging.物料') : this._translate.instant('accountsPayableAging.加工')
    }
    return item?.[key]
  }

  // 获取当前列表
  getList(reset = false) {
    this.tableConfig.loading = true;
    if (reset) {
      this.tableConfig.pageIndex = 1
    }
    const payload: any = {
      ...this.searchData,
      page: this.tableConfig.pageIndex,
      limit: this.tableConfig.pageSize
    }
    if (this.queryType == 2) {
      delete payload?.contract_number
      delete payload?.account_type
      delete payload?.age_day
      delete payload?.unpaid_money
    }
    const _serviceInterface = this.queryType == 1 ? this._service?.getList(payload) : this._service.getSumList(payload)
    _serviceInterface.subscribe((res) => {
      if (res.code == 200) {
        this.tableConfig.loading = false;
        if (this.queryType == 1) {
          res.data.data_list?.forEach((line: any) => {
            if (line?.payable_dates?.length) {
              line?.payable_dates?.forEach((item: any) => {
                const type = item?.receivable_age_type
                const key = `receivable_age_${type}`
                item[key] = item.receivable_age
              })
            } else {
              line.payable_dates = [null]
            }
          })
        }
        this.tableConfig.dataList = res.data.data_list;
        this.tableConfig.count = res.data?.total;
        this.tableConfig = { ...this.tableConfig };
      }
    })
  }

  // 计算高度
  calcTableHeight() {
    const bodyHeight = document.querySelector('.ant-layout-content')!.clientHeight;
    const searchBarHeight = this.searchBarWrap?.nativeElement?.clientHeight;
    const renderY = bodyHeight - searchBarHeight;
    const renderScrollY = renderY - 90 - 48 - 12;
    this.tableConfig.height = renderScrollY;
    this.tableConfig = { ...this.tableConfig };
    // 手动触发变更检测
    this._cdRef.detectChanges();
  }


  /**
   * 页数改变
   * @param  {number} e
   */
  onSizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }
   /**
   * 页码改变
   * @param  {number} e
   */
   onIndexChange(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }

}

# 出库详情组件修改总结

## 修改内容

### 1. 客户字段显示修改
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/components/product-List/product-list.config.ts`
- **修改**: 将客户字段的key从`customer`改为`customer_name`
- **目的**: 出库类型为5且出库清单选择按订单后列表显示的客户字段使用customer_name字段

### 2. 选择数据后客户字段设置修改
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/outstock-detail/detail.component.ts`
- **修改**: 修复第113行的错误，正确设置customer_id和customer_name字段
- **代码变更**:
  ```typescript
  // 修改前
  this.validateForm.get('customer_id')?.setValue(item.customer_id);
  this.validateForm.get('customer_id')?.setValue(item.customer_name); // 错误：重复设置customer_id

  // 修改后
  // 选择数据后，给表单中的客户添加customer_id和customer_name字段
  this.validateForm.get('customer_id')?.setValue(item.customer_id);
  this.validateForm.get('customer_name')?.setValue(item.customer_name);
  ```

### 3. 支付额度核验接口添加
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/outstock.service.ts`
- **修改**: 添加支付额度核验接口方法
- **新增方法**:
  ```typescript
  /**
   * 核验客户支付额度
   * @param params 
   */
  validPaymentQuota(params: { customer_id: string; currency_id: string }) {
    return this.http.post<any>('/service/procurement-inventory/archive/v1/customer/valid-payment-quota', params);
  }
  ```

### 4. 确认出库时支付额度核验
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/outstock-detail/detail.component.ts`
- **修改**: 在onSubmit方法中添加支付额度核验逻辑
- **核验条件**: 出库类型为5且出库清单选择按订单
- **核验流程**:
  1. 获取customer_id和currency_id
  2. 调用支付额度核验接口
  3. 如果valid为false，提示err信息并停止
  4. 如果valid为true，继续执行出库操作

### 5. 产品列表组件客户字段验证修改
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/components/product-List/product-list.component.ts`
- **修改**: 更新客户字段验证逻辑，兼容customer_name和customer字段
- **代码变更**:
  ```typescript
  // 修改前
  targetCustomer = this.disableList[0].customer;
  targetCustomer = list[0].customer;
  (v) => v.customer !== targetCustomer

  // 修改后
  targetCustomer = this.disableList[0].customer_name || this.disableList[0].customer;
  targetCustomer = list[0].customer_name || list[0].customer;
  (v) => (v.customer_name || v.customer) !== targetCustomer
  ```

### 6. 表单配置修改
- **文件**: `projects/fl-sewsmart-lib/product-inventory/product-out-stock/scm/out-stock/outstock-detail/detail.config.ts`
- **修改**: 更新客户字段配置，显示客户名称
- **变更**:
  - code从`customer_id`改为`customer_name`
  - type从`select`改为`input`
  - hide从`true`改为`false`

## 功能验证要点

1. **客户字段显示**: 在出库类型为5且按订单选择时，列表应显示customer_name字段
2. **数据设置**: 选择订单后，表单中应正确设置customer_id和customer_name
3. **支付额度核验**: 确认出库时应调用核验接口，根据返回结果决定是否继续
4. **错误处理**: 核验失败时应显示错误信息并停止操作
5. **兼容性**: 客户字段验证应兼容新旧数据格式

## 接口说明

### 支付额度核验接口
- **URL**: `/service/procurement-inventory/archive/v1/customer/valid-payment-quota`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "customer_id": "string", // 客户ID
    "currency_id": "string"  // 货币ID
  }
  ```
- **响应格式**:
  ```json
  {
    "valid": true,      // 是否通过核验，为true时为通过
    "err": "string"     // 为false时，会带上该err信息
  }
  ```

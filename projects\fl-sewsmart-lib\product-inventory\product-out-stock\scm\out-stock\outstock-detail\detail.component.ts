/* eslint-disable no-constant-condition */
import { ChangeDetectorRef, Component, Inject, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FlcDrawerHelperService, FlcValidatorService } from 'fl-common-lib';
import { FlcModalService } from 'fl-common-lib';
import { FlButtonType } from 'fl-ui-angular/button';
import { OutStockService } from '../outstock.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { finalize } from 'rxjs';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { formConfig, formReadConfig } from './detail.config';
import { FormConfig, OutboundInfo, StockListOption, UserActions } from '../models/outstock.interface';
import { OutboundOrderTypeEnum, OutboundStatusEnum, OutboundTypesEnum } from '../models/outstock.enum';
import { ProductListComponent } from '../components/product-List/product-list.component';
import { OutStockTableListComponent } from '../components/out-stock-table-list/out-stock-table-list.component';

interface BtnModel {
  type: FlButtonType;
  label: string;
  action?: any;
  icon?: string;
}
// @resizable()
@Component({
  selector: 'flss-stock-outstock-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class OutStockDetailComponent implements OnInit {
  @ViewChild(OutStockTableListComponent) outStockTableListCom?: OutStockTableListComponent;
  env_project = 'sewsmart'; //默认系统环境变量
  translateSuffix = this._service.translateSuffix; // 翻译
  _translate = this._service.translateFn;
  userActions!: UserActions; // 权限

  constructor(
    private _fb: FormBuilder,
    private _flcValidator: FlcValidatorService,
    private _router: Router,
    private _route: ActivatedRoute,
    private _service: OutStockService,
    private _msg: NzMessageService,
    private flcModalService: FlcModalService,
    private notification: NzNotificationService,
    private _flcDrawerService: FlcDrawerHelperService,
    private _cdr: ChangeDetectorRef,
    @Inject('environment') env: any
  ) {
    env.project && (this.env_project = env.project);
  }

  outboundTypesEnum = OutboundTypesEnum;
  isEdit = false;

  outbound_id: any; // 出库单id

  isSpinning = false; // 页面级别loading

  outboundOrderTypeEnum = OutboundOrderTypeEnum; // 按订单or按款号
  outboundOrderType?: OutboundOrderTypeEnum;

  // renderHeader = renderHeader;
  formConfig = formConfig;
  formReadConfig = formReadConfig;

  selectOption: StockListOption = {};

  outBoundInfo: OutboundInfo = {};

  validateForm: FormGroup = this._fb.group({
    code: ['', Validators.required],
    outbound_type: ['', Validators.required],
    warehouse_id: [null, Validators.required],
    warehouse_name: [null],
    remark: '',
    data_list: this._fb.array([]),
    product_type: [''],
    product_type_name: [{ value: '', disabled: true }],
    customer_id: [{ value: '', disabled: true }],
    customer_name: [{ value: '', disabled: true }],
  });

  get outbound_type() {
    return this.validateForm.get('outbound_type')?.value;
  }

  get outBoundList(): FormArray {
    return this.validateForm.get('data_list') as FormArray;
  }

  ngOnInit(): void {
    this.outbound_id = this._route.snapshot.paramMap.get('id');
    if (this.outbound_id === 'new') {
      this.isEdit = true;
      this.getOutboundCode();
    } else {
      this.getDetail(false);
    }
    this.getSelectOption();
    this.userActions = this._service.getUserActions();
    // (this as any).addResizePageListener();
    this.setFormConfig();

    this._flcDrawerService.eventEmit.subscribe((res: any) => {
      const item = res?.list[0];
      if (!item) return;
      this.validateForm.get('product_type')?.setValue(item.product_type);
      this.validateForm.get('product_type_name')?.setValue(item.product_type_name);
      this.validateForm.get('warehouse_id')?.setValue(item.warehouse_id, { emitViewToModelChange: false });
      this.validateForm.get('warehouse_name')?.setValue(item.warehouse_name, { emitViewToModelChange: false });
      // 选择数据后，给表单中的客户添加customer_id和customer_name字段
      this.validateForm.get('customer_id')?.setValue(item.customer_id);
      this.validateForm.get('customer_name')?.setValue(item.customer_name);
      this.outBoundInfo.warehouse_id = item.warehouse_id;
      this.outBoundInfo.warehouse_name = item.warehouse_name;
      this.outboundOrderType = res.outboundOrderType;
      const listIndex = this.outBoundList.length;
      res.list.forEach((ele: any, index: number) => {
        ele.outbound_qty = '';
        ele.box_qty = '';
        ele.box_number = '';
        ele.already_outbound_qty = ele.outbound_qty;
        this.outStockTableListCom && (this.outStockTableListCom.outboundOrderType = this.outboundOrderType);
        this.outStockTableListCom?.addOutBoundList(index + listIndex, ele, true);
      });
      this.validateForm?.markAsDirty();
      this._cdr.detectChanges();
    });
  }

  /**
   * 自动生成「出库单号」
   */
  getOutboundCode() {
    this._service.getOutboundCode().subscribe((ele) => {
      this.validateForm.get('code')?.setValue(ele.data?.code);
    });
  }

  /**
   * 获取「出库仓库」下拉数据
   */
  getSelectOption() {
    this._service.getSelectOption().subscribe((res) => {
      this.selectOption = res.data || {};
    });
  }

  /**
   * 获取详情
   */
  getDetail(change = true, isEdit = false) {
    this._service.isChange = change;
    this.isEdit = isEdit;
    this.isSpinning = true;

    this._service
      .getOutboundDetail(this.outbound_id)
      .pipe(finalize(() => (this.isSpinning = false)))
      .subscribe((res) => {
        this.outBoundList.clear();
        res.data.product_type_name = res.data.product_type === 1 ? '成品' : res.data.product_type === 3 ? '半成品' : '';
        this.outBoundInfo = JSON.parse(JSON.stringify(res.data));
        this.validateForm.patchValue(res.data);
        this.setFormConfig();
        this.outboundOrderType = res.data.data_type;
        setTimeout(() => {
          res.data?.data_list?.forEach((ele: any, index: number) => {
            this.outStockTableListCom?.addOutBoundList(index, ele);
          });
        }, 0);
      });
    this.validateForm?.markAsPristine();
    this.outBoundList?.markAsPristine();
  }

  /**
   * 处理保存提交出库单的参数
   * commit: 是否提交
   */
  formatParams(commit: boolean) {
    const payload = this.validateForm.getRawValue();
    const params: OutboundInfo = {
      ...payload,
      commit: commit,
      id: this.outbound_id === 'new' ? null : this.outbound_id,
      delete_ids: this.outStockTableListCom?.deleteLineIds,
      gen_terminal: this.outBoundInfo.gen_terminal,
      doc_codes: this.outBoundInfo.doc_codes,
      version: this.outBoundInfo.version,
      data_type: this.outbound_type === OutboundTypesEnum.Other ? 0 : this.outboundOrderType,
    };
    return params;
  }

  /**
   * 暂存出库单
   * */
  onSave() {
    if (!this.validateForm.get('code')?.value) {
      this.notification.create('error', this._translate('请填写出库单号'), '');
      return;
    }
    this.isSpinning = true;
    const params = this.formatParams(false);

    this._service
      .saveBound(params)
      .pipe(finalize(() => (this.isSpinning = false)))
      .subscribe((res) => {
        if (res.data.status) {
          if (this.outbound_id === 'new') {
            this.outbound_id = res.data.id;
            this.validateForm?.markAsPristine();
            this.outBoundList?.markAsPristine();
            this._router.navigate(['../', this.outbound_id], { relativeTo: this._route });
          }
          this._msg.success(this._translate('暂存成功', 'flss.success.'));
          this.getDetail(true, true);
        }
      });
  }

  /**
   * 校验表单
   */
  checkData(): boolean {
    if (this.outBoundList.length === 0) {
      this.notification.create('error', this._translate('请先选择出库清单'), '');
      return false;
    }

    this._flcValidator.formIsInvalid(this.validateForm);
    const invalid = this.validateForm.invalid;

    if (invalid) {
      return false;
    }

    for (const [index, ele] of this.outBoundList.value.entries()) {
      if (!ele.outbound_qty || ele.outbound_qty == '0') {
        this.notification.create('error', this._translate('validate_outbound_qty_tip', '', { index: index + 1 }), '');
        return false;
      }
      if (Number(ele.outbound_qty) > Number(ele.sku_wh_loc_qty)) {
        this.notification.create('error', this._translate('validate_sku_wh_loc_qty_tip', '', { index: index + 1 }), '');
        return false;
      }
    }

    return true;
  }

  /**
   * 提交出库单
   */
  onSubmit() {
    if (!this.checkData()) return;

    // 出库类型为OutboundTypesEnum.Sales且出库清单选择按订单后，点击确认出库时需要核验支付额度
    if (this.outbound_type === OutboundTypesEnum.Sales && this.outboundOrderType === this.outboundOrderTypeEnum.Order) {
      const customerId = this.validateForm.get('customer_id')?.value;
      const currencyId = this.outBoundList.value[0]?.currency_id; // 从出库清单中获取币种ID

      if (customerId && currencyId) {
        this.isSpinning = true;
        this._service.validPaymentQuota({ customer_id: customerId, currency_id: currencyId }).subscribe((res) => {
          this.isSpinning = false;
          if (res.data?.valid === false) {
            // 如果valid为false，提示err返回的信息并停止下面代码进行
            this._msg.error(res.data?.err || '支付额度核验失败');
            return;
          }
          // 为true则继续运行代码
          this.submitOutbound();
        }, (error) => {
          this.isSpinning = false;
          this._msg.error('支付额度核验失败，请稍后重试');
        });
        return;
      }
    }

    // 直接提交出库
    this.submitOutbound();
  }

  /**
   * 执行出库提交
   */
  private submitOutbound() {
    this.isSpinning = true;
    const params = this.formatParams(true);
    this._service
      .saveBound(params)
      .pipe(finalize(() => (this.isSpinning = false)))
      .subscribe((ele) => {
        if (ele.data.status) {
          if (this.outbound_id === 'new') {
            this.outbound_id = ele.data.id;
            this._router.navigate(['../', this.outbound_id], { relativeTo: this._route });
          }
          this.isEdit = false;
          this._msg.success(this._translate('出库成功'));
          this.getDetail();
        }
      });
  }

  /**
   * 删除出库单
   */
  onDelete() {
    const ref = this.flcModalService.confirmCancel({ content: this._translate('确定删除？') });
    ref.afterClose.subscribe((res) => {
      if (res) {
        const arg = {
          id: this.outbound_id,
        };
        this._service
          .deleteStock(arg)
          .pipe(
            finalize(() => {
              this.isSpinning = false;
            })
          )
          .subscribe((res) => {
            if (res.code === 200) {
              this._service.isChange = true;
              this.onBack();
            }
          });
      }
    });
  }

  onEdit() {
    this.isEdit = true;
  }

  onBack() {
    this._router.navigate(['../'], { relativeTo: this._route });
  }

  onCancel() {
    if (this.outbound_id === 'new') {
      this.onBack(); // 直接返回，让canLeave去控制
    } else {
      // 仅切换页面编辑状态
      if (this.validateForm?.dirty) {
        const ref = this.flcModalService.confirmCancel({ type: 'confirm-cancel' });
        ref.afterClose.subscribe((confirm: boolean) => {
          if (confirm) {
            this.getDetail();
            this.isEdit = false;
          }
        });
      } else {
        this.getDetail();
        this.isEdit = false;
      }
    }
  }

  onOpenDrawer(outboundOrderType: OutboundOrderTypeEnum) {
    this._flcDrawerService.openDrawer({
      title: outboundOrderType === OutboundOrderTypeEnum.Order ? '选择订单' : '选择款',
      content: ProductListComponent,
      height: '90%',
      contentParams: {
        warehouse_id: this.validateForm.get('warehouse_id')?.value,
        outbound_type: this.validateForm.get('outbound_type')?.value,
        product_type: this.validateForm.get('product_type')?.value,
        disableList: this.outBoundList.value,
        outboundOrderType: outboundOrderType,
      },
    });
  }

  /**
   * 切换出库仓库
   */
  onSelectSearch(e: number | string | undefined, item: FormConfig<OutboundInfo, StockListOption>) {
    if (item.code === 'warehouse_id') {
      this.onChangeWarehouse(e);
    } else if (item.code === 'outbound_type') {
      this.onChangeOutboundType(e);
    }
  }

  private onChangeOutboundType(e: number | string | undefined) {
    if (this.outBoundList.length) {
      const ref = this.flcModalService.confirmCancel({
        title: this._translate('提示'),
        subtitle: this._translate('切换丢失提示'),
        content: this._translate('确定切换出库类型？'),
        btnCenter: true,
        contentCenter: true,
      });
      ref.afterClose.subscribe((confirm: boolean) => {
        if (confirm) {
          this.outBoundInfo.outbound_type = e as string;
          this.outStockTableListCom?.logDeleteLineIds(this.outBoundList.value);
          this.outBoundList.clear();
          this.setFormConfig();
          this.outboundOrderType = undefined;
        } else {
          this.validateForm?.get('outbound_type')?.setValue(this.outBoundInfo.outbound_type, { emitViewToModelChange: false });
        }
      });
    } else {
      this.outBoundInfo.outbound_type = e as string;
      this.setFormConfig();
    }
  }

  private onChangeWarehouse(e: number | string | undefined) {
    const selectItem = this.selectOption.warehouse_ids?.find((ele) => ele.value === e);

    if (this.outBoundList.length) {
      const ref = this.flcModalService.confirmCancel({
        title: this._translate('提示'),
        subtitle: this._translate('切换丢失提示'),
        content: this._translate('确定切换出库仓库？'),
        btnCenter: true,
        contentCenter: true,
      });
      ref.afterClose.subscribe((confirm: boolean) => {
        if (confirm) {
          this.outBoundInfo.warehouse_id = selectItem?.value as string;
          this.outBoundInfo.warehouse_name = selectItem?.label;
          this.outStockTableListCom?.logDeleteLineIds(this.outBoundList.value);
          this.outBoundList.clear();
          this.outboundOrderType = undefined;
        } else {
          this.validateForm?.get('warehouse_id')?.setValue(this.outBoundInfo.warehouse_id, { emitViewToModelChange: false });
          this.validateForm?.get('warehouse_name')?.setValue(this.outBoundInfo.warehouse_name, { emitViewToModelChange: false });
        }
      });
    } else {
      this.outBoundInfo.warehouse_id = (selectItem?.value || '') as string;
      this.outBoundInfo.warehouse_name = selectItem?.label || '';
    }
  }

  canLeave() {
    if (this.isEdit) {
      return !this.validateForm?.dirty;
    } else {
      return true;
    }
  }

  buttonConfig: BtnModel[] = [
    { type: 'default', label: 'back', action: this.onBack.bind(this) },
    { type: 'default', label: 'cancel', action: this.onCancel.bind(this) },
    { type: 'pretty-minor', label: 'save_temp', action: this.onSave.bind(this) },
    { type: 'pretty-primary', label: 'add_outbound', action: this.onSubmit.bind(this) },
    { type: 'default-negative-danger', label: 'delete', action: this.onDelete.bind(this) },
    { type: 'pretty-primary', label: 'edit', icon: 'icon-caozuolan_bianji1', action: this.onEdit.bind(this) },
  ];

  // 1、未出库的pad按箱出库，可以查看和删除
  // 2、未出库的pad按件出库，可以查看和删除、编辑
  // 3、已出库的pad按箱/件出库，只能查看
  // 4、已出库的只能查看
  // 5、盘亏出库为盘点单提交时自动生成的，只能查看

  get btnList() {
    if (this.isEdit) {
      return this.userActions.create ? ['cancel', 'save_temp', 'add_outbound'] : ['back'];
    } else {
      const btns = ['back'];
      const is_wait_out = this.outBoundInfo.outbound_status == (OutboundStatusEnum['待出库'] as any);
      is_wait_out && this.userActions.create && btns.push('delete');
      this.outBoundInfo.edit && this.userActions.create && btns.push('edit');
      return btns;
    }
  }

  private setFormConfig() {
    const outbound_type = this.validateForm.get('outbound_type')?.value;
    const item = this.formConfig.find((ele) => ele.code === 'customer_id');
    const readItem = this.formReadConfig.find((ele) => ele.code === 'customer_name');
    item && (item.hide = outbound_type !== OutboundTypesEnum.Sales);
    readItem && (readItem.hide = outbound_type !== OutboundTypesEnum.Sales);
  }

  resizePage() {
    this.outStockTableListCom?.resizePage();
  }
}

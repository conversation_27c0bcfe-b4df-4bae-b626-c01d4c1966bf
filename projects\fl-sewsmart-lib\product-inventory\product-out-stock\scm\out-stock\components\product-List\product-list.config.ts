import { FlcTableHeaderConfig } from 'fl-common-lib';
import { ProductItem, ProductListOption, ProductSearchData, SearchConfig } from '../../models/outstock.interface';
import { OutboundOrderTypeEnum } from '../../models/outstock.enum';
//  表格搜索项
export function getSearchConfig(outboundOrderType: OutboundOrderTypeEnum) {
  const searchConfig: SearchConfig<ProductSearchData, ProductListOption>[] = [
    {
      label: '订单号',
      type: 'select',
      value: 'order_uuid',
      optionKey: 'order_uuids',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '款号',
      type: 'select',
      value: 'style_sku_ids',
      optionKey: 'style_skus',
    },
    {
      label: '品名',
      type: 'select',
      value: 'category_sku_ids',
      optionKey: 'category_skus',
    },
    {
      label: '客户',
      type: 'select',
      value: 'customer_id',
      optionKey: 'customers',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '产品类型',
      type: 'select',
      value: 'product_type',
      optionKey: 'product_types',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '供应商',
      type: 'select',
      value: 'supplier_sku_ids',
      optionKey: 'supplier_skus',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '供应商货号',
      type: 'select',
      value: 'supplier_art_num_sku_ids',
      optionKey: 'supplier_art_num_skus',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '加工厂',
      type: 'select',
      value: 'factory',
      optionKey: 'factory_list',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '颜色',
      type: 'select',
      value: 'color_sku_ids',
      optionKey: 'colors',
    },
    {
      label: '尺码',
      type: 'select',
      value: 'size_sku_ids',
      optionKey: 'sizes',
    },
    {
      label: '货位',
      type: 'locations',
      value: 'location',
      optionKey: 'locations',
    },
  ];
  return searchConfig.filter((item) => {
    return !item.modes || item.modes.includes(outboundOrderType);
  });
}

//表格字段
export type ProductItemTh = Partial<FlcTableHeaderConfig<ProductItem>> & { modes?: OutboundOrderTypeEnum[] };

const commonConfig = {
  visible: true,
  sort: false,
  pinned: false,
  disable: false,
  resizeble: true,
};
export function getTableHeaders(outboundOrderType: OutboundOrderTypeEnum): ProductItemTh[] {
  const listHeader: ProductItemTh[] = [
    {
      label: '订单号',
      key: 'order_code',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '币种',
      key: 'currency_name',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '交付单号',
      key: 'po_code',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '款号',
      key: 'style_code',
      type: 'text',
      width: '100px',
    },
    {
      label: '品名',
      key: 'category',
      type: 'text',
      width: '100px',
    },
    {
      label: '客户',
      key: 'customer_name', // 出库类型为5且出库清单选择按订单后列表显示的客户字段使用customer_name字段
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '加工厂',
      key: 'factory',
      type: 'text',
      width: '150px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '产品类型',
      key: 'product_type_name',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '供应商',
      key: 'supplier_name',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '供应商货号',
      key: 'supplier_art_num',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Style],
    },
    {
      label: '颜色',
      key: 'color',
      type: 'template',
      templateName: 'color',
      width: '100px',
    },
    {
      label: '尺码',
      key: 'size',
      type: 'template',
      templateName: 'size',
      width: '100px',
    },
    {
      label: '订单数',
      key: 'order_qty',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '溢装数',
      key: 'over_qty',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '可出库数',
      key: 'allow_outbound_qty',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '已出库数',
      key: 'outbound_qty',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Order],
    },
    {
      label: '仓库',
      key: 'warehouse_name',
      type: 'text',
      width: '100px',
    },
    {
      label: '货位',
      key: 'location',
      type: 'template',
      templateName: 'location',
      width: '180px',
    },
    {
      label: '库存数量',
      key: 'inventory_qty',
      type: 'text',
      width: '100px',
      modes: [OutboundOrderTypeEnum.Style],
    },
  ];

  return listHeader.map((item) => ({ ...commonConfig, ...item })).filter((item) => !item.modes || item.modes.includes(outboundOrderType));
}
